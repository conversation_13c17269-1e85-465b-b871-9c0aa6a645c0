"""
Flask-SocketIO API Routes
========================

API endpoints for triggering SocketIO events and managing connections.
These routes provide REST API access to SocketIO functionality.
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# Create blueprint for SocketIO API routes
socketio_api_bp = Blueprint('socketio_api', __name__, url_prefix='/api/socketio')

@socketio_api_bp.route('/connections', methods=['GET'])
def get_connections():
    """Get connection statistics"""
    try:
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager and socketio_manager.socketio:
            stats = {
                'active_users': len(socketio_manager.active_users),
                'guest_sessions': len(socketio_manager.guest_sessions),
                'admin_sessions': len(socketio_manager.admin_sessions),
                'total_connections': len(socketio_manager.active_users) + len(socketio_manager.guest_sessions),
                'timestamp': datetime.now().isoformat(),
                'status': 'active'
            }
            return jsonify(stats)
        else:
            return jsonify({
                'error': 'SocketIO manager not available',
                'status': 'inactive',
                'timestamp': datetime.now().isoformat()
            }), 503
            
    except Exception as e:
        logger.error(f"Failed to get connection stats: {e}")
        return jsonify({'error': f'Failed to get stats: {str(e)}'}), 500

@socketio_api_bp.route('/broadcast', methods=['POST'])
def broadcast_message():
    """Broadcast a general message to all connected clients"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager and socketio_manager.socketio:
            message = {
                'type': 'broadcast',
                'message': data.get('message', ''),
                'timestamp': datetime.now().isoformat(),
                'sender': 'system'
            }
            
            socketio_manager.socketio.emit('broadcast', message)
            
            return jsonify({
                'status': 'Message broadcasted successfully',
                'message': message
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to broadcast message: {e}")
        return jsonify({'error': f'Broadcast failed: {str(e)}'}), 500

@socketio_api_bp.route('/notify-user/<user_id>', methods=['POST'])
def notify_user(user_id):
    """Send notification to specific user"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            socketio_manager.send_notification(user_id, data)
            
            return jsonify({
                'status': 'Notification sent successfully',
                'user_id': user_id,
                'data': data
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to send notification: {e}")
        return jsonify({'error': f'Notification failed: {str(e)}'}), 500

# Event trigger endpoints
@socketio_api_bp.route('/events/inventory-update', methods=['POST'])
def trigger_inventory_update():
    """Trigger inventory update event"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        new_quantity = data.get('new_quantity')
        old_quantity = data.get('old_quantity')
        
        if product_id is None or new_quantity is None:
            return jsonify({'error': 'Missing required fields: product_id, new_quantity'}), 400
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            socketio_manager.broadcast_inventory_update(product_id, new_quantity, old_quantity)
            
            return jsonify({
                'status': 'Inventory update broadcasted',
                'product_id': product_id,
                'new_quantity': new_quantity,
                'old_quantity': old_quantity
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to broadcast inventory update: {e}")
        return jsonify({'error': f'Failed to broadcast: {str(e)}'}), 500

@socketio_api_bp.route('/events/price-update', methods=['POST'])
def trigger_price_update():
    """Trigger price update event"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        new_price = data.get('new_price')
        old_price = data.get('old_price')
        
        if not all([product_id, new_price is not None, old_price is not None]):
            return jsonify({'error': 'Missing required fields: product_id, new_price, old_price'}), 400
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            socketio_manager.broadcast_price_update(product_id, new_price, old_price)
            
            return jsonify({
                'status': 'Price update broadcasted',
                'product_id': product_id,
                'new_price': new_price,
                'old_price': old_price
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to broadcast price update: {e}")
        return jsonify({'error': f'Failed to broadcast: {str(e)}'}), 500

@socketio_api_bp.route('/events/cart-update', methods=['POST'])
def trigger_cart_update():
    """Trigger cart update event"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        session_id = data.get('session_id')
        cart_data = data.get('cart_data', {})
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            socketio_manager.send_cart_update(user_id, session_id, cart_data)
            
            return jsonify({
                'status': 'Cart update sent',
                'user_id': user_id,
                'session_id': session_id
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to send cart update: {e}")
        return jsonify({'error': f'Failed to send update: {str(e)}'}), 500

@socketio_api_bp.route('/events/order-update', methods=['POST'])
def trigger_order_update():
    """Trigger order status update event"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        order_id = data.get('order_id')
        status = data.get('status')
        
        if not all([user_id, order_id, status]):
            return jsonify({'error': 'Missing required fields: user_id, order_id, status'}), 400
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            socketio_manager.send_order_status_update(user_id, order_id, status)
            
            return jsonify({
                'status': 'Order update sent',
                'user_id': user_id,
                'order_id': order_id,
                'order_status': status
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to send order update: {e}")
        return jsonify({'error': f'Failed to send update: {str(e)}'}), 500

@socketio_api_bp.route('/admin/broadcast', methods=['POST'])
def admin_broadcast():
    """Broadcast message to admin users only"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            message = {
                'type': 'admin_broadcast',
                'message': data.get('message', ''),
                'timestamp': datetime.now().isoformat(),
                'sender': 'admin'
            }
            
            socketio_manager.broadcast_to_admins(message)
            
            return jsonify({
                'status': 'Admin broadcast sent',
                'message': message
            })
        else:
            return jsonify({'error': 'SocketIO manager not available'}), 503
        
    except Exception as e:
        logger.error(f"Failed to send admin broadcast: {e}")
        return jsonify({'error': f'Failed to broadcast: {str(e)}'}), 500

@socketio_api_bp.route('/health', methods=['GET'])
def socketio_health():
    """Check SocketIO system health"""
    try:
        # Import here to avoid circular imports
        from flask_socketio_manager import socketio_manager
        
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'socketio_available': socketio_manager is not None,
            'socketio_instance': socketio_manager.socketio is not None if socketio_manager else False,
            'redis_available': socketio_manager.redis_client is not None if socketio_manager else False,
            'active_connections': {
                'users': len(socketio_manager.active_users) if socketio_manager else 0,
                'guests': len(socketio_manager.guest_sessions) if socketio_manager else 0,
                'admins': len(socketio_manager.admin_sessions) if socketio_manager else 0
            }
        }
        
        # Test Redis connection if available
        if socketio_manager and socketio_manager.redis_client:
            try:
                socketio_manager.redis_client.ping()
                health_status['redis_status'] = 'connected'
            except:
                health_status['redis_status'] = 'disconnected'
        else:
            health_status['redis_status'] = 'not_configured'
        
        # Overall health
        if health_status['socketio_available'] and health_status['socketio_instance']:
            health_status['status'] = 'healthy'
            return jsonify(health_status)
        else:
            health_status['status'] = 'unhealthy'
            return jsonify(health_status), 503
        
    except Exception as e:
        logger.error(f"SocketIO health check failed: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
