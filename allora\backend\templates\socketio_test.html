<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Allora Flask-SocketIO Test Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin: 5px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-connected { background: #28a745; }
        .status-disconnected { background: #dc3545; }
        .status-connecting { background: #ffc107; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #dee2e6;
        }
        
        .panel h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-success { background: linear-gradient(135deg, #27ae60 0%, #229954 100%); }
        .btn-warning { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); }
        .btn-danger { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .log-container {
            grid-column: 1 / -1;
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 25px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-container h3 {
            color: #ecf0f1;
            border-bottom: 2px solid #3498db;
        }
        
        .log-entry {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .log-info { background: rgba(52, 152, 219, 0.2); }
        .log-success { background: rgba(39, 174, 96, 0.2); }
        .log-warning { background: rgba(243, 156, 18, 0.2); }
        .log-error { background: rgba(231, 76, 60, 0.2); }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 Allora Flask-SocketIO Test Dashboard</h1>
            <p>Real-time WebSocket Testing & Monitoring</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot status-disconnected" id="connectionStatus"></div>
                <span id="connectionText">Disconnected</span>
            </div>
            <div class="status-item">
                <strong>Server:</strong> <span id="serverUrl">http://localhost:5000</span>
            </div>
            <div class="status-item">
                <strong>Session ID:</strong> <span id="sessionId">Not connected</span>
            </div>
            <div class="status-item">
                <strong>User ID:</strong> <span id="userId">Guest</span>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Connection Panel -->
            <div class="panel">
                <h3>🔗 Connection Control</h3>
                <div class="input-group">
                    <label for="userIdInput">User ID (optional):</label>
                    <input type="text" id="userIdInput" placeholder="Enter user ID or leave empty for guest">
                </div>
                <div class="input-group">
                    <label for="isAdminInput">Admin User:</label>
                    <select id="isAdminInput">
                        <option value="false">Regular User</option>
                        <option value="true">Admin User</option>
                    </select>
                </div>
                <button class="btn btn-success" onclick="connectSocket()">🔌 Connect</button>
                <button class="btn btn-danger" onclick="disconnectSocket()">🔌 Disconnect</button>
                <button class="btn" onclick="sendPing()">📡 Ping Server</button>
            </div>
            
            <!-- Event Testing Panel -->
            <div class="panel">
                <h3>🧪 Event Testing</h3>
                <div class="input-group">
                    <label for="productId">Product ID:</label>
                    <input type="number" id="productId" value="1" min="1">
                </div>
                <div class="input-group">
                    <label for="quantity">Quantity:</label>
                    <input type="number" id="quantity" value="10" min="0">
                </div>
                <button class="btn btn-warning" onclick="testInventoryUpdate()">📦 Test Inventory Update</button>
                <button class="btn btn-warning" onclick="testPriceUpdate()">💰 Test Price Update</button>
                <button class="btn btn-warning" onclick="testCartUpdate()">🛒 Test Cart Update</button>
                <button class="btn btn-warning" onclick="testNotification()">🔔 Test Notification</button>
            </div>
            
            <!-- Subscription Panel -->
            <div class="panel">
                <h3>📡 Event Subscriptions</h3>
                <button class="btn" onclick="subscribeToEvents(['inventory', 'prices', 'orders'])">📦 Subscribe to Inventory</button>
                <button class="btn" onclick="subscribeToEvents(['prices'])">💰 Subscribe to Prices</button>
                <button class="btn" onclick="subscribeToEvents(['orders'])">📋 Subscribe to Orders</button>
                <button class="btn" onclick="subscribeToEvents(['inventory', 'prices', 'orders'])">🔔 Subscribe to All</button>
            </div>
            
            <!-- Statistics Panel -->
            <div class="panel">
                <h3>📊 Connection Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="messagesReceived">0</div>
                        <div class="stat-label">Messages Received</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="messagesSent">0</div>
                        <div class="stat-label">Messages Sent</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="connectionTime">0s</div>
                        <div class="stat-label">Connected Time</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="reconnectCount">0</div>
                        <div class="stat-label">Reconnections</div>
                    </div>
                </div>
            </div>
            
            <!-- Real-time Log -->
            <div class="log-container">
                <h3>📝 Real-time Event Log</h3>
                <div id="logContainer">
                    <div class="log-entry log-info">
                        <strong>[INFO]</strong> WebSocket test dashboard loaded. Click "Connect" to start testing.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let socket = null;
        let connectionStartTime = null;
        let messagesReceived = 0;
        let messagesSent = 0;
        let reconnectCount = 0;
        let connectionTimer = null;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateConnectionStatus(status, text) {
            const statusDot = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            statusDot.className = `status-dot status-${status}`;
            statusText.textContent = text;
        }

        function updateStats() {
            document.getElementById('messagesReceived').textContent = messagesReceived;
            document.getElementById('messagesSent').textContent = messagesSent;
            document.getElementById('reconnectCount').textContent = reconnectCount;
        }

        function startConnectionTimer() {
            connectionStartTime = Date.now();
            connectionTimer = setInterval(() => {
                if (connectionStartTime) {
                    const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                    document.getElementById('connectionTime').textContent = `${elapsed}s`;
                }
            }, 1000);
        }

        function stopConnectionTimer() {
            if (connectionTimer) {
                clearInterval(connectionTimer);
                connectionTimer = null;
            }
            connectionStartTime = null;
            document.getElementById('connectionTime').textContent = '0s';
        }

        function connectSocket() {
            if (socket && socket.connected) {
                log('Already connected to server', 'warning');
                return;
            }

            const userId = document.getElementById('userIdInput').value.trim();
            const isAdmin = document.getElementById('isAdminInput').value === 'true';

            log('Attempting to connect to Flask-SocketIO server...', 'info');
            updateConnectionStatus('connecting', 'Connecting...');

            // Create socket connection
            socket = io('http://localhost:5000', {
                auth: {
                    user_id: userId || null,
                    is_admin: isAdmin
                }
            });

            // Connection events
            socket.on('connect', () => {
                log('✅ Successfully connected to Flask-SocketIO server!', 'success');
                updateConnectionStatus('connected', 'Connected');
                document.getElementById('sessionId').textContent = socket.id;
                document.getElementById('userId').textContent = userId || 'Guest';
                startConnectionTimer();
            });

            socket.on('disconnect', () => {
                log('❌ Disconnected from server', 'error');
                updateConnectionStatus('disconnected', 'Disconnected');
                document.getElementById('sessionId').textContent = 'Not connected';
                stopConnectionTimer();
            });

            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`, 'error');
                updateConnectionStatus('disconnected', 'Connection Error');
            });

            socket.on('reconnect', () => {
                reconnectCount++;
                log('🔄 Reconnected to server', 'success');
                updateStats();
            });

            // Custom events
            socket.on('connection_established', (data) => {
                messagesReceived++;
                log(`🎉 Connection established: ${JSON.stringify(data)}`, 'success');
                updateStats();
            });

            socket.on('pong', (data) => {
                messagesReceived++;
                log(`🏓 Pong received: ${JSON.stringify(data)}`, 'info');
                updateStats();
            });

            socket.on('inventory_update', (data) => {
                messagesReceived++;
                log(`📦 Inventory update: Product ${data.product_id} - Quantity: ${data.new_quantity}`, 'info');
                updateStats();
            });

            socket.on('price_update', (data) => {
                messagesReceived++;
                log(`💰 Price update: Product ${data.product_id} - New price: $${data.new_price}`, 'info');
                updateStats();
            });

            socket.on('cart_update', (data) => {
                messagesReceived++;
                log(`🛒 Cart update received: ${JSON.stringify(data)}`, 'info');
                updateStats();
            });

            socket.on('order_update', (data) => {
                messagesReceived++;
                log(`📋 Order update: Order ${data.order_id} - Status: ${data.status}`, 'info');
                updateStats();
            });

            socket.on('notification', (data) => {
                messagesReceived++;
                log(`🔔 Notification: ${JSON.stringify(data)}`, 'info');
                updateStats();
            });

            socket.on('admin_notification', (data) => {
                messagesReceived++;
                log(`👑 Admin notification: ${JSON.stringify(data)}`, 'warning');
                updateStats();
            });

            socket.on('subscribed', (data) => {
                messagesReceived++;
                log(`📡 Subscribed to events: ${data.events.join(', ')}`, 'success');
                updateStats();
            });

            socket.on('heartbeat_ack', (data) => {
                messagesReceived++;
                log(`💓 Heartbeat acknowledged: ${data.timestamp}`, 'info');
                updateStats();
            });
        }

        function disconnectSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('🔌 Manually disconnected from server', 'info');
                stopConnectionTimer();
            }
        }

        function sendPing() {
            if (!socket || !socket.connected) {
                log('❌ Not connected to server', 'error');
                return;
            }
            socket.emit('ping');
            messagesSent++;
            log('🏓 Ping sent to server', 'info');
            updateStats();
        }

        function subscribeToEvents(events) {
            if (!socket || !socket.connected) {
                log('❌ Not connected to server', 'error');
                return;
            }
            socket.emit('subscribe', { events: events });
            messagesSent++;
            log(`📡 Subscription request sent for: ${events.join(', ')}`, 'info');
            updateStats();
        }

        function testInventoryUpdate() {
            const productId = document.getElementById('productId').value;
            const quantity = document.getElementById('quantity').value;
            
            // Simulate inventory update (this would normally come from backend)
            if (socket && socket.connected) {
                log(`🧪 Testing inventory update for product ${productId} with quantity ${quantity}`, 'warning');
                // This simulates what the backend would send
                socket.emit('test_inventory_update', {
                    product_id: parseInt(productId),
                    new_quantity: parseInt(quantity),
                    old_quantity: parseInt(quantity) - 5
                });
                messagesSent++;
                updateStats();
            } else {
                log('❌ Not connected to server', 'error');
            }
        }

        function testPriceUpdate() {
            const productId = document.getElementById('productId').value;
            
            if (socket && socket.connected) {
                log(`🧪 Testing price update for product ${productId}`, 'warning');
                socket.emit('test_price_update', {
                    product_id: parseInt(productId),
                    new_price: 29.99,
                    old_price: 34.99
                });
                messagesSent++;
                updateStats();
            } else {
                log('❌ Not connected to server', 'error');
            }
        }

        function testCartUpdate() {
            if (socket && socket.connected) {
                log('🧪 Testing cart update', 'warning');
                socket.emit('test_cart_update', {
                    cart_data: {
                        items: 3,
                        total: 89.97,
                        updated_at: new Date().toISOString()
                    }
                });
                messagesSent++;
                updateStats();
            } else {
                log('❌ Not connected to server', 'error');
            }
        }

        function testNotification() {
            if (socket && socket.connected) {
                log('🧪 Testing notification', 'warning');
                socket.emit('test_notification', {
                    title: 'Test Notification',
                    message: 'This is a test notification from the SocketIO dashboard',
                    type: 'info'
                });
                messagesSent++;
                updateStats();
            } else {
                log('❌ Not connected to server', 'error');
            }
        }

        // Initialize stats
        updateStats();
        
        // Auto-connect on page load (optional)
        // connectSocket();
    </script>
</body>
</html>
