absl-py==2.2.2
astunparse==1.6.3
blinker==1.9.0
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.1.8
colorama==0.4.6
Flask==2.3.2
Flask-Cors==4.0.0
Flask-SQLAlchemy==3.0.5
Flask-Bcrypt==1.0.1
flatbuffers==25.2.10
gast==0.6.0
google-pasta==0.2.0
greenlet==3.2.1
grpcio==1.71.0
h5py==3.13.0
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
joblib==1.4.2
keras==3.9.2
libclang==18.1.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
ml_dtypes==0.5.1
mysql-connector-python==8.0.33
namex==0.0.9
numpy==2.1.3
opt_einsum==3.4.0
optree==0.15.0
packaging==25.0
pandas==2.2.3
pillow==11.2.1
protobuf==3.20.3
Pygments==2.19.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
pytz==2025.2
requests==2.32.3
rich==14.0.0
scikit-learn==1.6.1
scipy==1.15.2
six==1.17.0
SQLAlchemy==2.0.40
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorflow==2.19.0
tensorflow-io-gcs-filesystem==0.31.0
termcolor==3.1.0
threadpoolctl==3.6.0
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
Werkzeug==3.1.3
wrapt==1.17.2
gunicorn==20.1.0

# OAuth and Social Authentication
google-auth==2.35.0
google-auth-oauthlib==1.2.1
google-auth-httplib2==0.2.0
requests-oauthlib==2.0.0
PyJWT==2.10.1

# Additional security and validation
email-validator==2.2.0
phonenumbers==8.13.52

# Elasticsearch and Search
elasticsearch==8.15.1
elasticsearch-dsl==8.15.0

# Task Scheduling
schedule==1.2.2

# Redis and Caching
redis==5.0.1
flask-session==0.5.0
cachelib==0.13.0

# Monitoring and Error Tracking
sentry-sdk[flask]==1.40.6
psutil==5.9.8

# WebSocket Support
flask-socketio==5.3.6
python-socketio==5.11.0
