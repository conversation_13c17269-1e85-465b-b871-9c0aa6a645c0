"""
User Behavior Tracking API
==========================

REST API endpoints for user behavior tracking and analytics.
Provides endpoints for tracking interactions, analyzing behavior patterns,
and managing user sessions.

Author: Allora Development Team
Date: 2025-07-06
"""

from flask import Blueprint, request, jsonify, session
from datetime import datetime, timedelta
import json
import uuid
import sys
import os
from typing import Dict, List, Optional, Any

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Import behavior tracker and types (models will be imported lazily)
from .user_behavior_tracker import BehaviorTracker
from .recommendation_system_architecture import UserInteractionType

# Database models will be imported lazily to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession
        return db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession
    except ImportError as e:
        # Return None values if import fails
        return None, None, None, None, None, None

def safe_db_operation(func):
    """Decorator to safely handle database operations with lazy imports"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return jsonify({'error': f'Database operation failed: {str(e)}'}), 500
    return wrapper

# Create Blueprint
behavior_api = Blueprint('behavior_api', __name__)

# Initialize behavior tracker (will be set up in main app)
behavior_tracker = None

def init_behavior_tracker(db_session, redis_client):
    """Initialize the behavior tracker with database and Redis connections"""
    global behavior_tracker
    behavior_tracker = BehaviorTracker(db_session, redis_client)

@behavior_api.route('/api/behavior/track', methods=['POST'])
def track_interaction():
    """Track a user interaction"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['user_id', 'product_id', 'interaction_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate interaction type
        try:
            interaction_type = UserInteractionType(data['interaction_type'])
        except ValueError:
            return jsonify({'error': 'Invalid interaction type'}), 400
        
        # Extract optional fields
        session_id = data.get('session_id')
        value = data.get('value')
        context = data.get('context', {})
        metadata = data.get('metadata', {})
        
        # Track interaction using behavior tracker
        if behavior_tracker:
            interaction_id = behavior_tracker.track_interaction(
                user_id=str(data['user_id']),
                product_id=str(data['product_id']),
                interaction_type=interaction_type,
                session_id=session_id,
                value=value,
                context=context,
                metadata=metadata
            )
        else:
            interaction_id = None
        
        # Also store in database for persistence
        db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession = get_models()

        interaction_log = UserInteractionLog(
            user_id=data['user_id'],
            product_id=data['product_id'] if data['product_id'] != 'page_view' else None,
            session_id=session_id or str(uuid.uuid4()),
            interaction_type=data['interaction_type'],
            interaction_value=value,
            context_data=context,
            interaction_metadata=metadata
        )

        db.session.add(interaction_log)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'interaction_id': interaction_id,
            'db_id': interaction_log.id,
            'message': 'Interaction tracked successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to track interaction: {str(e)}'}), 500

@behavior_api.route('/api/behavior/track/page-view', methods=['POST'])
def track_page_view():
    """Track page view interaction"""
    try:
        data = request.get_json()
        
        required_fields = ['user_id', 'page_type', 'page_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Track using behavior tracker
        if behavior_tracker:
            interaction_id = behavior_tracker.track_page_view(
                user_id=str(data['user_id']),
                page_type=data['page_type'],
                page_id=str(data['page_id']),
                session_id=data.get('session_id'),
                duration=data.get('duration'),
                context=data.get('context', {})
            )
        else:
            interaction_id = None
        
        return jsonify({
            'success': True,
            'interaction_id': interaction_id,
            'message': 'Page view tracked successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to track page view: {str(e)}'}), 500

@behavior_api.route('/api/behavior/track/search', methods=['POST'])
def track_search():
    """Track search interaction"""
    try:
        data = request.get_json()
        
        required_fields = ['user_id', 'search_query', 'results_count']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Track using behavior tracker
        if behavior_tracker:
            interaction_id = behavior_tracker.track_search_interaction(
                user_id=str(data['user_id']),
                search_query=data['search_query'],
                results_count=data['results_count'],
                clicked_products=data.get('clicked_products', []),
                session_id=data.get('session_id')
            )
        else:
            interaction_id = None
        
        return jsonify({
            'success': True,
            'interaction_id': interaction_id,
            'message': 'Search interaction tracked successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to track search: {str(e)}'}), 500

@behavior_api.route('/api/behavior/track/cart', methods=['POST'])
def track_cart_interaction():
    """Track cart interaction (add/remove)"""
    try:
        data = request.get_json()
        
        required_fields = ['user_id', 'product_id', 'action']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        if data['action'] not in ['add', 'remove']:
            return jsonify({'error': 'Action must be "add" or "remove"'}), 400
        
        # Track using behavior tracker
        if behavior_tracker:
            interaction_id = behavior_tracker.track_cart_interaction(
                user_id=str(data['user_id']),
                product_id=str(data['product_id']),
                action=data['action'],
                quantity=data.get('quantity', 1),
                session_id=data.get('session_id')
            )
        else:
            interaction_id = None
        
        return jsonify({
            'success': True,
            'interaction_id': interaction_id,
            'message': 'Cart interaction tracked successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to track cart interaction: {str(e)}'}), 500

@behavior_api.route('/api/behavior/track/purchase', methods=['POST'])
def track_purchase():
    """Track purchase interaction"""
    try:
        data = request.get_json()
        
        required_fields = ['user_id', 'order_id', 'products', 'total_amount']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Track using behavior tracker
        if behavior_tracker:
            interaction_ids = behavior_tracker.track_purchase(
                user_id=str(data['user_id']),
                order_id=str(data['order_id']),
                products=data['products'],
                total_amount=float(data['total_amount']),
                session_id=data.get('session_id')
            )
        else:
            interaction_ids = []
        
        return jsonify({
            'success': True,
            'interaction_ids': interaction_ids,
            'message': 'Purchase tracked successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to track purchase: {str(e)}'}), 500

@behavior_api.route('/api/behavior/track/compare', methods=['POST'])
def track_compare_interaction():
    """Track product comparison interaction"""
    try:
        data = request.get_json()

        required_fields = ['user_id', 'product_ids']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Validate product_ids is a list with at least 2 products
        product_ids = data['product_ids']
        if not isinstance(product_ids, list) or len(product_ids) < 2:
            return jsonify({'error': 'product_ids must be a list with at least 2 products'}), 400

        # Track using behavior tracker
        if behavior_tracker:
            interaction_ids = behavior_tracker.track_compare_interaction(
                user_id=str(data['user_id']),
                product_ids=[str(pid) for pid in product_ids],
                comparison_type=data.get('comparison_type', 'product_comparison'),
                session_id=data.get('session_id'),
                context=data.get('context', {})
            )
        else:
            interaction_ids = []

        # Also store in database for persistence
        db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession = get_models()

        db_ids = []
        for product_id in product_ids:
            interaction_log = UserInteractionLog(
                user_id=data['user_id'],
                product_id=product_id,
                session_id=data.get('session_id') or str(uuid.uuid4()),
                interaction_type='compare',
                interaction_value=len(product_ids),  # Number of products compared
                context_data={
                    'comparison_type': data.get('comparison_type', 'product_comparison'),
                    'products_compared': product_ids,
                    'comparison_count': len(product_ids)
                },
                interaction_metadata=data.get('context', {})
            )

            db.session.add(interaction_log)
            db.session.flush()
            db_ids.append(interaction_log.id)

        db.session.commit()

        return jsonify({
            'success': True,
            'interaction_ids': interaction_ids,
            'db_ids': db_ids,
            'products_compared': len(product_ids),
            'message': 'Product comparison tracked successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to track comparison: {str(e)}'}), 500

@behavior_api.route('/api/behavior/user/<int:user_id>/interactions', methods=['GET'])
def get_user_interactions(user_id):
    """Get user interaction history"""
    try:
        # Query parameters
        limit = min(request.args.get('limit', 100, type=int), 500)
        interaction_types = request.args.getlist('types')

        # Get interactions using behavior tracker
        if behavior_tracker:
            filter_types = [UserInteractionType(t) for t in interaction_types] if interaction_types else None
            interactions = behavior_tracker.get_user_interactions(
                str(user_id),
                limit=limit,
                interaction_types=filter_types
            )

            # Convert to serializable format
            interactions_data = []
            for interaction in interactions:
                interactions_data.append({
                    'user_id': interaction.user_id,
                    'product_id': interaction.product_id,
                    'interaction_type': interaction.interaction_type.value,
                    'timestamp': interaction.timestamp.isoformat(),
                    'session_id': interaction.session_id,
                    'value': interaction.value,
                    'context': interaction.context,
                    'metadata': interaction.metadata
                })
        else:
            # Fallback to database query
            interactions_data = _get_user_interactions_db(user_id, limit, interaction_types)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'interactions': interactions_data,
            'count': len(interactions_data)
        })

    except Exception as e:
        return jsonify({'error': f'Failed to get user interactions: {str(e)}'}), 500

@behavior_api.route('/api/behavior/user/<int:user_id>/analysis', methods=['GET'])
def analyze_user_behavior(user_id):
    """Get comprehensive user behavior analysis"""
    try:
        if behavior_tracker:
            analysis = behavior_tracker.analyze_user_behavior(str(user_id))
        else:
            # Fallback analysis using database
            analysis = _analyze_user_behavior_db(user_id)

        return jsonify(analysis)

    except Exception as e:
        return jsonify({'error': f'Failed to analyze user behavior: {str(e)}'}), 500

@behavior_api.route('/api/behavior/user/<int:user_id>/profile', methods=['GET'])
def get_user_behavior_profile(user_id):
    """Get user behavior profile"""
    try:
        db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession = get_models()

        profile = UserBehaviorProfile.query.filter_by(user_id=user_id).first()

        if not profile:
            return jsonify({'error': 'User behavior profile not found'}), 404

        return jsonify(profile.to_dict())

    except Exception as e:
        return jsonify({'error': f'Failed to get user profile: {str(e)}'}), 500

@behavior_api.route('/api/behavior/user/<int:user_id>/profile', methods=['PUT'])
def update_user_behavior_profile(user_id):
    """Update user behavior profile"""
    try:
        data = request.get_json()
        db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession = get_models()

        profile = UserBehaviorProfile.query.filter_by(user_id=user_id).first()

        if not profile:
            # Create new profile
            profile = UserBehaviorProfile(user_id=user_id)
            db.session.add(profile)

        # Update profile fields
        if 'preference_scores' in data:
            profile.preference_scores = data['preference_scores']
        if 'category_preferences' in data:
            profile.category_preferences = data['category_preferences']
        if 'brand_preferences' in data:
            profile.brand_preferences = data['brand_preferences']
        if 'price_sensitivity' in data:
            profile.price_sensitivity = data['price_sensitivity']
        if 'engagement_score' in data:
            profile.engagement_score = data['engagement_score']

        profile.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'User behavior profile updated successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to update user profile: {str(e)}'}), 500

def _get_user_interactions_db(user_id: int, limit: int, interaction_types: List[str]) -> List[Dict]:
    """Fallback function to get interactions from database"""
    try:
        db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession = get_models()

        query = UserInteractionLog.query.filter_by(user_id=user_id)

        if interaction_types:
            query = query.filter(UserInteractionLog.interaction_type.in_(interaction_types))

        interactions = query.order_by(UserInteractionLog.timestamp.desc()).limit(limit).all()

        return [interaction.to_dict() for interaction in interactions]

    except Exception as e:
        print(f"Error getting interactions from database: {e}")
        return []

def _analyze_user_behavior_db(user_id: int) -> Dict[str, Any]:
    """Fallback behavior analysis using database queries"""
    try:
        db, User, Product, UserInteractionLog, UserBehaviorProfile, UserSession = get_models()

        # Get user interactions from database
        interactions = UserInteractionLog.query.filter_by(user_id=user_id).order_by(
            UserInteractionLog.timestamp.desc()
        ).limit(500).all()

        if not interactions:
            return {'error': 'No interactions found'}

        # Basic analysis
        total_interactions = len(interactions)
        interaction_types = {}
        products_interacted = set()

        for interaction in interactions:
            interaction_types[interaction.interaction_type] = interaction_types.get(
                interaction.interaction_type, 0
            ) + 1
            if interaction.product_id:
                products_interacted.add(interaction.product_id)

        return {
            'user_id': user_id,
            'total_interactions': total_interactions,
            'unique_products': len(products_interacted),
            'interaction_breakdown': interaction_types,
            'last_activity': interactions[0].timestamp.isoformat() if interactions else None
        }

    except Exception as e:
        return {'error': f'Database analysis failed: {str(e)}'}

# Export the blueprint and initialization function
__all__ = ['behavior_api', 'init_behavior_tracker']
