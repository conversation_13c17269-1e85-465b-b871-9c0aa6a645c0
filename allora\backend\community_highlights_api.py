"""
Community Highlights API
========================

Real community features for home page:
- Recent community posts/reviews
- Sustainability Stories from users
- Featured eco-friendly brands/sellers
- Admin-configurable community highlights

Author: Allora Development Team
Date: 2025-07-11
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Set up logging
logger = logging.getLogger(__name__)

# Create blueprint with versioning
community_highlights_bp = Blueprint('community_highlights', __name__, url_prefix='/api/v1/community-highlights')

# Database models will be imported lazily to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import (
            db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
            ProductReview, Hashtag, PostHashtag, CommunityStats
        )
        return (
            db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
            ProductReview, Hashtag, PostHashtag, CommunityStats
        )
    except ImportError:
        return tuple([None] * 10)

# ============================================================================
# RECENT COMMUNITY POSTS
# ============================================================================

@community_highlights_bp.route('/recent-posts', methods=['GET'])
def get_recent_community_posts():
    """Get recent community posts for home page highlights"""
    try:
        (db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
         ProductReview, Hashtag, PostHashtag, CommunityStats) = get_models()
        
        if not db:
            return jsonify({'error': 'Database not available'}), 500
        
        limit = request.args.get('limit', 6, type=int)
        post_type = request.args.get('type', 'all')  # all, photo, review, sustainability
        
        # Build query for recent posts
        query = db.session.query(
            CommunityPost,
            User.first_name,
            User.last_name,
            User.username,
            db.func.count(PostLike.id).label('like_count'),
            db.func.count(PostComment.id).label('comment_count')
        ).join(
            User, CommunityPost.user_id == User.id
        ).outerjoin(
            PostLike, CommunityPost.id == PostLike.post_id
        ).outerjoin(
            PostComment, CommunityPost.id == PostComment.post_id
        )
        
        # Filter by post type if specified
        if post_type != 'all':
            if post_type == 'sustainability':
                # Posts with sustainability-related hashtags
                sustainability_hashtags = ['#sustainability', '#eco', '#green', '#sustainable', '#ecofriendly']
                query = query.join(
                    PostHashtag, CommunityPost.id == PostHashtag.post_id
                ).join(
                    Hashtag, PostHashtag.hashtag_id == Hashtag.id
                ).filter(
                    Hashtag.tag.in_(sustainability_hashtags)
                )
            else:
                query = query.filter(CommunityPost.post_type == post_type)
        
        # Get recent posts with engagement metrics
        recent_posts = query.group_by(
            CommunityPost.id
        ).order_by(
            CommunityPost.created_at.desc()
        ).limit(limit).all()
        
        posts_data = []
        for post, first_name, last_name, username, like_count, comment_count in recent_posts:
            # Get hashtags for this post
            hashtags = db.session.query(Hashtag.tag).join(
                PostHashtag, Hashtag.id == PostHashtag.hashtag_id
            ).filter(
                PostHashtag.post_id == post.id
            ).all()
            
            posts_data.append({
                'id': post.id,
                'content': post.content,
                'post_type': post.post_type,
                'image_url': post.image_url,
                'video_url': post.video_url,
                'created_at': post.created_at.isoformat(),
                'user': {
                    'id': post.user_id,
                    'first_name': first_name,
                    'last_name': last_name,
                    'username': username,
                    'display_name': f"{first_name} {last_name}" if first_name and last_name else username
                },
                'engagement': {
                    'like_count': int(like_count),
                    'comment_count': int(comment_count),
                    'engagement_score': int(like_count) + int(comment_count) * 2
                },
                'hashtags': [tag[0] for tag in hashtags],
                'highlight_reason': 'Recent community activity'
            })
        
        return jsonify({
            'success': True,
            'data': posts_data,
            'algorithm': 'recent_community_posts',
            'total_count': len(posts_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting recent community posts: {e}")
        return jsonify({'error': 'Failed to get recent community posts'}), 500

# ============================================================================
# SUSTAINABILITY STORIES
# ============================================================================

@community_highlights_bp.route('/sustainability-stories', methods=['GET'])
def get_sustainability_stories():
    """Get sustainability stories from users"""
    try:
        (db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
         ProductReview, Hashtag, PostHashtag, CommunityStats) = get_models()
        
        if not db:
            return jsonify({'error': 'Database not available'}), 500
        
        limit = request.args.get('limit', 4, type=int)
        
        # Get posts with sustainability hashtags and high engagement
        sustainability_hashtags = ['sustainability', 'eco', 'green', 'sustainable', 'ecofriendly', 'zerowaste', 'climateaction']
        
        sustainability_stories = db.session.query(
            CommunityPost,
            User.first_name,
            User.last_name,
            User.username,
            db.func.count(PostLike.id).label('like_count'),
            db.func.count(PostComment.id).label('comment_count')
        ).join(
            User, CommunityPost.user_id == User.id
        ).join(
            PostHashtag, CommunityPost.id == PostHashtag.post_id
        ).join(
            Hashtag, PostHashtag.hashtag_id == Hashtag.id
        ).outerjoin(
            PostLike, CommunityPost.id == PostLike.post_id
        ).outerjoin(
            PostComment, CommunityPost.id == PostComment.post_id
        ).filter(
            Hashtag.tag.in_(sustainability_hashtags),
            CommunityPost.post_type.in_(['photo', 'review', 'text'])
        ).group_by(
            CommunityPost.id
        ).order_by(
            (db.func.count(PostLike.id) + db.func.count(PostComment.id) * 2).desc(),
            CommunityPost.created_at.desc()
        ).limit(limit).all()
        
        stories_data = []
        for post, first_name, last_name, username, like_count, comment_count in sustainability_stories:
            # Get all hashtags for this post
            hashtags = db.session.query(Hashtag.tag).join(
                PostHashtag, Hashtag.id == PostHashtag.hashtag_id
            ).filter(
                PostHashtag.post_id == post.id
            ).all()
            
            stories_data.append({
                'id': post.id,
                'content': post.content,
                'post_type': post.post_type,
                'image_url': post.image_url,
                'created_at': post.created_at.isoformat(),
                'user': {
                    'id': post.user_id,
                    'first_name': first_name,
                    'last_name': last_name,
                    'username': username,
                    'display_name': f"{first_name} {last_name}" if first_name and last_name else username
                },
                'engagement': {
                    'like_count': int(like_count),
                    'comment_count': int(comment_count),
                    'engagement_score': int(like_count) + int(comment_count) * 2
                },
                'hashtags': [tag[0] for tag in hashtags],
                'story_type': 'sustainability',
                'highlight_reason': 'Inspiring sustainability story'
            })
        
        return jsonify({
            'success': True,
            'data': stories_data,
            'algorithm': 'sustainability_stories',
            'total_count': len(stories_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting sustainability stories: {e}")
        return jsonify({'error': 'Failed to get sustainability stories'}), 500

# ============================================================================
# FEATURED ECO-FRIENDLY BRANDS/SELLERS
# ============================================================================

@community_highlights_bp.route('/featured-brands', methods=['GET'])
def get_featured_eco_brands():
    """Get featured eco-friendly brands/sellers"""
    try:
        (db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
         ProductReview, Hashtag, PostHashtag, CommunityStats) = get_models()
        
        if not db:
            return jsonify({'error': 'Database not available'}), 500
        
        limit = request.args.get('limit', 3, type=int)
        
        # Get sellers with high sustainability scores and good ratings
        featured_sellers = db.session.query(
            Seller,
            db.func.avg(Product.sustainability_score).label('avg_sustainability'),
            db.func.count(Product.id).label('product_count'),
            db.func.avg(ProductReview.rating).label('avg_rating'),
            db.func.count(ProductReview.id).label('review_count')
        ).join(
            Product, Seller.id == Product.seller_id
        ).outerjoin(
            ProductReview, Product.id == ProductReview.product_id
        ).group_by(
            Seller.id
        ).having(
            db.func.avg(Product.sustainability_score) >= 80,
            db.func.count(Product.id) >= 3
        ).order_by(
            db.func.avg(Product.sustainability_score).desc(),
            db.func.avg(ProductReview.rating).desc()
        ).limit(limit).all()
        
        brands_data = []
        for seller, avg_sustainability, product_count, avg_rating, review_count in featured_sellers:
            # Get some featured products from this seller
            featured_products = Product.query.filter_by(
                seller_id=seller.id
            ).order_by(
                Product.sustainability_score.desc(),
                Product.average_rating.desc()
            ).limit(3).all()
            
            brands_data.append({
                'id': seller.id,
                'business_name': seller.business_name,
                'store_name': seller.store_name,
                'store_description': seller.store_description,
                'store_logo': seller.store_logo,
                'is_verified': seller.is_verified,
                'sustainability_metrics': {
                    'avg_sustainability_score': round(float(avg_sustainability), 1) if avg_sustainability else 0,
                    'product_count': int(product_count),
                    'avg_rating': round(float(avg_rating), 1) if avg_rating else 0,
                    'review_count': int(review_count) if review_count else 0
                },
                'featured_products': [{
                    'id': product.id,
                    'name': product.name,
                    'price': float(product.price) if product.price else 0,
                    'image': product.image,
                    'sustainability_score': product.sustainability_score
                } for product in featured_products],
                'highlight_reason': f'High sustainability score ({round(float(avg_sustainability), 1)})',
                'eco_badges': _get_eco_badges(avg_sustainability)
            })
        
        return jsonify({
            'success': True,
            'data': brands_data,
            'algorithm': 'featured_eco_brands',
            'total_count': len(brands_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting featured eco brands: {e}")
        return jsonify({'error': 'Failed to get featured eco brands'}), 500

def _get_eco_badges(sustainability_score):
    """Get eco badges based on sustainability score"""
    badges = []
    if sustainability_score >= 95:
        badges.extend(['🌟 Eco Champion', '🌱 Carbon Neutral'])
    elif sustainability_score >= 90:
        badges.extend(['🌿 Eco Leader', '♻️ Sustainable'])
    elif sustainability_score >= 80:
        badges.extend(['🌱 Eco Friendly'])
    return badges

# ============================================================================
# RECENT PRODUCT REVIEWS
# ============================================================================

@community_highlights_bp.route('/recent-reviews', methods=['GET'])
def get_recent_product_reviews():
    """Get recent product reviews for community highlights"""
    try:
        (db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
         ProductReview, Hashtag, PostHashtag, CommunityStats) = get_models()
        
        if not db:
            return jsonify({'error': 'Database not available'}), 500
        
        limit = request.args.get('limit', 4, type=int)
        
        # Get recent high-quality reviews
        recent_reviews = db.session.query(
            ProductReview,
            User.first_name,
            User.last_name,
            User.username,
            Product.name.label('product_name'),
            Product.image.label('product_image'),
            Product.sustainability_score
        ).join(
            User, ProductReview.user_id == User.id
        ).join(
            Product, ProductReview.product_id == Product.id
        ).filter(
            ProductReview.rating >= 4,
            ProductReview.comment.isnot(None),
            db.func.length(ProductReview.comment) >= 50  # Substantial reviews
        ).order_by(
            ProductReview.created_at.desc()
        ).limit(limit).all()
        
        reviews_data = []
        for review, first_name, last_name, username, product_name, product_image, sustainability_score in recent_reviews:
            reviews_data.append({
                'id': review.id,
                'rating': review.rating,
                'title': review.title,
                'comment': review.comment,
                'verified_purchase': review.verified_purchase,
                'helpful_count': review.helpful_count,
                'created_at': review.created_at.isoformat(),
                'user': {
                    'id': review.user_id,
                    'first_name': first_name,
                    'last_name': last_name,
                    'username': username,
                    'display_name': f"{first_name} {last_name}" if first_name and last_name else username
                },
                'product': {
                    'id': review.product_id,
                    'name': product_name,
                    'image': product_image,
                    'sustainability_score': sustainability_score
                },
                'highlight_reason': 'High-quality recent review'
            })
        
        return jsonify({
            'success': True,
            'data': reviews_data,
            'algorithm': 'recent_product_reviews',
            'total_count': len(reviews_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting recent reviews: {e}")
        return jsonify({'error': 'Failed to get recent reviews'}), 500

# Export blueprint
def register_community_highlights_api(app):
    """Register community highlights API with Flask app"""
    app.register_blueprint(community_highlights_bp)
    logger.info("Community Highlights API registered successfully")
