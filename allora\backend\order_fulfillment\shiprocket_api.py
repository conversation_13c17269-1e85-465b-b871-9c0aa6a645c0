#!/usr/bin/env python3
"""
Shiprocket API Integration for Allora Backend
============================================

Complete Shiprocket shipping integration with support for:
- Multi-carrier rate calculation
- Shipment creation and management
- Real-time tracking
- Pickup scheduling
- Webhook notifications

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import requests
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

# Import base classes from existing carrier integration
from .carrier_integration import (
    BaseCarrierAPI, CarrierConfig, CarrierAPIError,
    Address, Package, ShippingRate, TrackingEvent, TrackingEventType
)

logger = logging.getLogger(__name__)

class ShiprocketServiceType(Enum):
    """Shiprocket service types"""
    SURFACE = "surface"
    EXPRESS = "express"
    SAME_DAY = "same_day"
    NEXT_DAY = "next_day"

@dataclass
class ShiprocketCredentials:
    """Shiprocket API credentials"""
    email: str
    password: str
    token: Optional[str] = None
    token_expires: Optional[datetime] = None
    sandbox: bool = True

class ShiprocketAPI(BaseCarrierAPI):
    """Shiprocket API implementation for multi-carrier shipping"""
    
    def __init__(self, config: CarrierConfig):
        super().__init__(config)
        
        # Initialize credentials
        self.credentials = ShiprocketCredentials(
            email=self.config.credentials.get('email', ''),
            password=self.config.credentials.get('password', ''),
            sandbox=self.config.credentials.get('sandbox', True)
        )
        
        # Set base URL based on environment
        if self.credentials.sandbox:
            self.base_url = "https://staging-apiv2.shiprocket.in/v1"
        else:
            self.base_url = "https://apiv2.shiprocket.in/v1"
        
        # Default pickup location
        self.default_pickup_location = self.config.credentials.get('default_pickup_location', 'primary')
        
        logger.info(f"Shiprocket API initialized - Sandbox: {self.credentials.sandbox}")
    
    def authenticate(self) -> bool:
        """Authenticate with Shiprocket API and get access token"""
        try:
            # Check if token is still valid
            if (self.credentials.token and self.credentials.token_expires and 
                datetime.now() < self.credentials.token_expires):
                return True
            
            auth_url = f"{self.base_url}/external/auth/login"
            auth_data = {
                "email": self.credentials.email,
                "password": self.credentials.password
            }
            
            response = requests.post(auth_url, json=auth_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                self.credentials.token = data.get('token')
                # Shiprocket tokens typically expire in 10 days
                self.credentials.token_expires = datetime.now() + timedelta(days=10)
                
                logger.info("Shiprocket authentication successful")
                return True
            else:
                logger.error(f"Shiprocket authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Shiprocket authentication error: {str(e)}")
            return False
    
    def _get_headers(self) -> Dict[str, str]:
        """Get API headers with authentication"""
        if not self.authenticate():
            raise CarrierAPIError("Authentication failed", "shiprocket")
        
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.credentials.token}'
        }
    
    def calculate_rates(self, origin: Address, destination: Address, 
                       packages: List[Package]) -> List[ShippingRate]:
        """Calculate shipping rates using Shiprocket serviceability API"""
        try:
            # Calculate total weight and declared value
            total_weight = sum(pkg.weight for pkg in packages)
            total_value = sum(pkg.declared_value for pkg in packages)
            
            # Shiprocket serviceability endpoint
            url = f"{self.base_url}/external/courier/serviceability/"
            
            params = {
                'pickup_postcode': origin.postal_code,
                'delivery_postcode': destination.postal_code,
                'weight': total_weight,
                'cod': 1 if total_value > 0 else 0,  # Assume COD if declared value > 0
                'declared_value': total_value
            }
            
            response = requests.get(url, params=params, headers=self._get_headers(), timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                rates = []
                
                if data.get('status') == 200:
                    courier_data = data.get('data', {})
                    available_couriers = courier_data.get('available_courier_companies', [])
                    
                    for courier in available_couriers:
                        # Create shipping rate for each available courier
                        base_rate = float(courier.get('rate', 0))
                        cod_charges = float(courier.get('cod_charges', 0))
                        total_rate = base_rate + cod_charges
                        
                        rates.append(ShippingRate(
                            carrier='shiprocket',
                            service_type=courier.get('courier_name', 'Standard'),
                            service_name=f"Shiprocket - {courier.get('courier_name', 'Standard')}",
                            cost=total_rate,
                            currency='INR',
                            estimated_days=int(courier.get('estimated_delivery_days', 3)),
                            guaranteed=courier.get('courier_name', '').lower() in ['blue_dart', 'fedex'],
                            pickup_required=True,
                            signature_required=True,
                            insurance_included=courier.get('freight_charge', 0) > 0,
                            tracking_included=True,
                            cod_available=courier.get('cod', 0) == 1,
                            additional_info={
                                'courier_company_id': courier.get('courier_company_id'),
                                'courier_name': courier.get('courier_name'),
                                'base_rate': base_rate,
                                'cod_charges': cod_charges,
                                'freight_charge': courier.get('freight_charge', 0),
                                'other_charges': courier.get('other_charges', 0)
                            }
                        ))
                
                # Sort by cost (ascending)
                rates.sort(key=lambda x: x.cost)
                
                logger.info(f"Shiprocket rates calculated: {len(rates)} options found")
                return rates
            else:
                logger.error(f"Shiprocket rate calculation failed: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Shiprocket rate calculation error: {str(e)}")
            raise CarrierAPIError(f"Rate calculation failed: {str(e)}", "shiprocket")
    
    def create_shipment(self, origin: Address, destination: Address,
                       packages: List[Package], service_type: str = 'surface',
                       reference_number: str = None, **kwargs) -> Dict[str, Any]:
        """Create shipment using Shiprocket API"""
        try:
            # Prepare order data for Shiprocket
            order_data = self._prepare_order_data(
                origin, destination, packages, service_type, reference_number, **kwargs
            )
            
            # Create order in Shiprocket
            url = f"{self.base_url}/external/orders/create/adhoc"
            response = requests.post(url, json=order_data, headers=self._get_headers(), timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('status_code') == 1:
                    order_id = data.get('order_id')
                    shipment_id = data.get('shipment_id')
                    
                    # Generate AWB if auto_awb is enabled
                    awb_result = None
                    if kwargs.get('auto_awb', True) and kwargs.get('courier_company_id'):
                        awb_result = self._generate_awb(shipment_id, kwargs['courier_company_id'])
                    
                    return {
                        'success': True,
                        'order_id': order_id,
                        'shipment_id': shipment_id,
                        'tracking_number': awb_result.get('awb_code') if awb_result else None,
                        'label_url': awb_result.get('label_url') if awb_result else None,
                        'carrier_reference': shipment_id,
                        'estimated_delivery': self._calculate_delivery_date(service_type),
                        'shipping_cost': order_data.get('sub_total', 0),
                        'shiprocket_data': {
                            'order_id': order_id,
                            'shipment_id': shipment_id,
                            'awb_data': awb_result
                        }
                    }
                else:
                    error_msg = data.get('message', 'Unknown error')
                    raise CarrierAPIError(f"Shipment creation failed: {error_msg}", "shiprocket")
            else:
                logger.error(f"Shiprocket shipment creation failed: {response.status_code} - {response.text}")
                raise CarrierAPIError("Shipment creation failed", "shiprocket")
                
        except Exception as e:
            logger.error(f"Shiprocket shipment creation error: {str(e)}")
            raise CarrierAPIError(f"Shipment creation failed: {str(e)}", "shiprocket")
    
    def _prepare_order_data(self, origin: Address, destination: Address,
                           packages: List[Package], service_type: str,
                           reference_number: str, **kwargs) -> Dict[str, Any]:
        """Prepare order data for Shiprocket API"""
        
        # Calculate totals
        total_weight = sum(pkg.weight for pkg in packages)
        total_value = sum(pkg.declared_value for pkg in packages)
        
        # Get customer info from kwargs
        customer_info = kwargs.get('customer_info', {})
        
        # Prepare order items
        order_items = []
        for i, pkg in enumerate(packages):
            order_items.append({
                "name": pkg.description or f"Item {i+1}",
                "sku": pkg.sku or f"SKU{i+1}",
                "units": 1,
                "selling_price": pkg.declared_value,
                "discount": 0,
                "tax": 0,
                "hsn": pkg.hsn_code or 123456
            })
        
        # Prepare Shiprocket order data
        order_data = {
            "order_id": reference_number or f"ORD-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "order_date": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "pickup_location": self.default_pickup_location,
            "billing_customer_name": destination.name,
            "billing_last_name": customer_info.get('last_name', ''),
            "billing_address": destination.address_line_1,
            "billing_address_2": destination.address_line_2 or '',
            "billing_city": destination.city,
            "billing_pincode": destination.postal_code,
            "billing_state": destination.state,
            "billing_country": destination.country or 'India',
            "billing_email": destination.email or customer_info.get('email', ''),
            "billing_phone": destination.phone,
            "shipping_is_billing": True,
            "order_items": order_items,
            "payment_method": kwargs.get('payment_method', 'COD'),
            "shipping_charges": kwargs.get('shipping_charges', 0),
            "giftwrap_charges": 0,
            "transaction_charges": 0,
            "total_discount": kwargs.get('discount', 0),
            "sub_total": total_value,
            "length": max(pkg.length for pkg in packages) if packages else 10,
            "breadth": max(pkg.width for pkg in packages) if packages else 10,
            "height": max(pkg.height for pkg in packages) if packages else 10,
            "weight": total_weight
        }
        
        return order_data

    def _generate_awb(self, shipment_id: int, courier_company_id: int) -> Dict[str, Any]:
        """Generate AWB for shipment"""
        try:
            url = f"{self.base_url}/external/courier/assign/awb"

            data = {
                'shipment_id': shipment_id,
                'courier_id': courier_company_id
            }

            response = requests.post(url, json=data, headers=self._get_headers(), timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status_code') == 1:
                    awb_data = result.get('response', {}).get('data', {})
                    return {
                        'success': True,
                        'awb_code': awb_data.get('awb_code'),
                        'courier_company_id': awb_data.get('courier_company_id'),
                        'courier_name': awb_data.get('courier_name'),
                        'label_url': awb_data.get('label_url'),
                        'pickup_scheduled': awb_data.get('pickup_scheduled_date')
                    }
                else:
                    logger.error(f"AWB generation failed: {result.get('message')}")
                    return {'success': False, 'error': result.get('message')}
            else:
                logger.error(f"AWB generation request failed: {response.status_code}")
                return {'success': False, 'error': 'AWB generation request failed'}

        except Exception as e:
            logger.error(f"AWB generation error: {str(e)}")
            return {'success': False, 'error': str(e)}

    def track_shipment(self, tracking_number: str) -> List[TrackingEvent]:
        """Track shipment using Shiprocket tracking API"""
        try:
            url = f"{self.base_url}/external/courier/track/awb/{tracking_number}"

            response = requests.get(url, headers=self._get_headers(), timeout=30)

            if response.status_code == 200:
                data = response.json()
                events = []

                if data.get('status_code') == 1:
                    tracking_data = data.get('tracking_data', {})
                    shipment_track = tracking_data.get('shipment_track', [])

                    for track_info in shipment_track:
                        for activity in track_info.get('shipment_track_activities', []):
                            events.append(TrackingEvent(
                                event_type=self._map_status_to_event_type(activity.get('activity', '')),
                                status=activity.get('activity', ''),
                                description=activity.get('activity', ''),
                                location=activity.get('location', ''),
                                timestamp=self._parse_timestamp(activity.get('date', '')),
                                carrier_code=track_info.get('courier_name', 'SR')[:2].upper()
                            ))

                # Sort events by timestamp
                events.sort(key=lambda x: x.timestamp)

                logger.info(f"Shiprocket tracking retrieved: {len(events)} events")
                return events
            else:
                logger.error(f"Shiprocket tracking failed: {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"Shiprocket tracking error: {str(e)}")
            raise CarrierAPIError(f"Tracking failed: {str(e)}", "shiprocket")

    def cancel_shipment(self, tracking_number: str) -> bool:
        """Cancel shipment using Shiprocket API"""
        try:
            url = f"{self.base_url}/external/orders/cancel"

            data = {
                'awbs': [tracking_number]
            }

            response = requests.post(url, json=data, headers=self._get_headers(), timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status_code') == 1:
                    logger.info(f"Shiprocket shipment cancelled: {tracking_number}")
                    return True
                else:
                    logger.error(f"Shipment cancellation failed: {result.get('message')}")
                    return False
            else:
                logger.error(f"Cancellation request failed: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Shiprocket cancellation error: {str(e)}")
            return False

    def schedule_pickup(self, pickup_address: Address, packages: List[Package],
                       pickup_date: datetime, time_window: str = None) -> Dict[str, Any]:
        """Schedule pickup using Shiprocket API"""
        try:
            # Get shipment IDs that need pickup (this would typically come from your database)
            # For now, we'll return a placeholder implementation

            url = f"{self.base_url}/external/courier/generate/pickup"

            data = {
                'shipment_id': [],  # List of shipment IDs
                'pickup_date': pickup_date.strftime('%Y-%m-%d')
            }

            response = requests.post(url, json=data, headers=self._get_headers(), timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status_code') == 1:
                    return {
                        'success': True,
                        'pickup_id': result.get('pickup_id'),
                        'pickup_date': pickup_date.date(),
                        'time_window': time_window or "10:00-17:00",
                        'pickup_token': result.get('pickup_token')
                    }
                else:
                    raise CarrierAPIError(f"Pickup scheduling failed: {result.get('message')}", "shiprocket")
            else:
                raise CarrierAPIError("Pickup scheduling request failed", "shiprocket")

        except Exception as e:
            logger.error(f"Shiprocket pickup scheduling error: {str(e)}")
            raise CarrierAPIError(f"Pickup scheduling failed: {str(e)}", "shiprocket")

    def get_pickup_locations(self) -> Dict[str, Any]:
        """Get configured pickup locations"""
        try:
            url = f"{self.base_url}/external/settings/company/pickup"

            response = requests.get(url, headers=self._get_headers(), timeout=30)

            if response.status_code == 200:
                data = response.json()
                if data.get('status_code') == 1:
                    return {
                        'success': True,
                        'data': data.get('data', {})
                    }
                else:
                    return {'success': False, 'error': data.get('message')}
            else:
                return {'success': False, 'error': 'Failed to get pickup locations'}

        except Exception as e:
            logger.error(f"Get pickup locations error: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _map_status_to_event_type(self, status: str) -> TrackingEventType:
        """Map Shiprocket status to standard tracking event type"""
        status_lower = status.lower()

        if 'picked' in status_lower:
            return TrackingEventType.PICKED_UP
        elif 'transit' in status_lower or 'forwarded' in status_lower:
            return TrackingEventType.IN_TRANSIT
        elif 'out for delivery' in status_lower:
            return TrackingEventType.OUT_FOR_DELIVERY
        elif 'delivered' in status_lower:
            return TrackingEventType.DELIVERED
        elif 'exception' in status_lower or 'failed' in status_lower:
            return TrackingEventType.EXCEPTION
        elif 'returned' in status_lower or 'rto' in status_lower:
            return TrackingEventType.RETURNED
        elif 'manifest' in status_lower or 'booked' in status_lower:
            return TrackingEventType.LABEL_CREATED
        else:
            return TrackingEventType.IN_TRANSIT

    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp from Shiprocket API"""
        try:
            # Shiprocket typically uses format: "2024-01-15 14:30:00"
            return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # Alternative format: "2024-01-15T14:30:00Z"
                if timestamp_str.endswith('Z'):
                    timestamp_str = timestamp_str[:-1]
                return datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S")
            except ValueError:
                # Fallback to current time if parsing fails
                logger.warning(f"Could not parse timestamp: {timestamp_str}")
                return datetime.now()

    def _calculate_delivery_date(self, service_type: str) -> datetime:
        """Calculate estimated delivery date based on service type"""
        days_map = {
            'same_day': 0,
            'next_day': 1,
            'express': 1,
            'surface': 3
        }

        days = days_map.get(service_type.lower(), 3)
        return datetime.now() + timedelta(days=days)


# Utility functions for Shiprocket integration
def format_order_for_shiprocket(order_data: Dict) -> Dict:
    """Format order data for Shiprocket API"""

    # Extract required fields
    order_id = order_data.get('order_id')
    customer = order_data.get('customer', {})
    items = order_data.get('items', [])
    shipping_address = order_data.get('shipping_address', {})

    # Format items for Shiprocket
    formatted_items = []
    for item in items:
        formatted_items.append({
            "name": item.get('name', ''),
            "sku": item.get('sku', ''),
            "units": item.get('quantity', 1),
            "selling_price": item.get('price', 0),
            "discount": item.get('discount', 0),
            "tax": item.get('tax', 0),
            "hsn": item.get('hsn', 123456)
        })

    # Calculate totals
    sub_total = sum(item.get('price', 0) * item.get('quantity', 1) for item in items)

    # Format for Shiprocket
    shiprocket_order = {
        "order_id": str(order_id),
        "order_date": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "pickup_location": "primary",
        "billing_customer_name": customer.get('name', ''),
        "billing_last_name": customer.get('last_name', ''),
        "billing_address": shipping_address.get('address', ''),
        "billing_address_2": shipping_address.get('address_2', ''),
        "billing_city": shipping_address.get('city', ''),
        "billing_pincode": shipping_address.get('pincode', ''),
        "billing_state": shipping_address.get('state', ''),
        "billing_country": shipping_address.get('country', 'India'),
        "billing_email": customer.get('email', ''),
        "billing_phone": customer.get('phone', ''),
        "shipping_is_billing": True,
        "order_items": formatted_items,
        "payment_method": order_data.get('payment_method', 'COD'),
        "shipping_charges": order_data.get('shipping_charges', 0),
        "giftwrap_charges": 0,
        "transaction_charges": 0,
        "total_discount": order_data.get('discount', 0),
        "sub_total": sub_total,
        "length": order_data.get('package', {}).get('length', 10),
        "breadth": order_data.get('package', {}).get('width', 10),
        "height": order_data.get('package', {}).get('height', 10),
        "weight": order_data.get('package', {}).get('weight', 0.5)
    }

    return shiprocket_order
