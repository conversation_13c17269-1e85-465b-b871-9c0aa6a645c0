"""
Automated Inventory Synchronization Scheduler
Handles background sync operations, retry mechanisms, and scheduled tasks
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json
import time
from threading import Thread
import schedule

# Use lazy imports to avoid circular dependencies
# All imports from app.py will be done inside functions when needed

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InventorySyncScheduler:
    """Main scheduler class for inventory synchronization"""

    def __init__(self):
        self.running = False
        self.sync_threads = {}
        self.max_concurrent_syncs = 5
        self.retry_delays = [30, 60, 300, 900, 3600]  # Exponential backoff in seconds

        # Cache for lazy-loaded components
        self._app = None
        self._db = None
        self._models = None
        self._functions = None

    def _get_app_components(self):
        """Get Flask app and database with caching"""
        if self._app is None or self._db is None:
            from app import app, db
            self._app, self._db = app, db
        return self._app, self._db

    def _get_models(self):
        """Get database models with caching"""
        if self._models is None:
            try:
                from app import (
                    SalesChannel, ChannelInventory, InventorySyncLog, SyncQueue,
                    InventoryConflict, Product, ProductVariant
                )
                self._models = {
                    'SalesChannel': SalesChannel,
                    'ChannelInventory': ChannelInventory,
                    'InventorySyncLog': InventorySyncLog,
                    'SyncQueue': SyncQueue,
                    'InventoryConflict': InventoryConflict,
                    'Product': Product,
                    'ProductVariant': ProductVariant
                }
            except (ImportError, NameError) as e:
                logger.error(f"Failed to import models: {e}")
                # Return empty dict to prevent further errors
                self._models = {}
        return self._models

    def _get_functions(self):
        """Get inventory functions with caching"""
        if self._functions is None:
            from app import InventoryUpdateManager, queue_inventory_sync, detect_inventory_conflicts
            self._functions = (InventoryUpdateManager, queue_inventory_sync, detect_inventory_conflicts)
        return self._functions
        
    def start(self):
        """Start the scheduler"""
        logger.info("Starting Inventory Sync Scheduler...")
        self.running = True
        
        # Schedule periodic tasks
        schedule.every(1).minutes.do(self.process_sync_queue)
        schedule.every(5).minutes.do(self.check_scheduled_syncs)
        schedule.every(15).minutes.do(self.retry_failed_syncs)
        schedule.every(30).minutes.do(self.cleanup_old_logs)
        schedule.every(1).hours.do(self.detect_and_resolve_conflicts)
        schedule.every(6).hours.do(self.health_check)
        
        # Start scheduler thread
        scheduler_thread = Thread(target=self._run_scheduler, daemon=True)
        scheduler_thread.start()
        
        # Start queue processor
        queue_thread = Thread(target=self._run_queue_processor, daemon=True)
        queue_thread.start()
        
        logger.info("Inventory Sync Scheduler started successfully")
    
    def stop(self):
        """Stop the scheduler"""
        logger.info("Stopping Inventory Sync Scheduler...")
        self.running = False
        schedule.clear()
        logger.info("Inventory Sync Scheduler stopped")
    
    def _run_scheduler(self):
        """Run the main scheduler loop"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(10)  # Check every 10 seconds
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _run_queue_processor(self):
        """Run the queue processor loop"""
        while self.running:
            try:
                self.process_sync_queue()
                time.sleep(5)  # Process queue every 5 seconds
            except Exception as e:
                logger.error(f"Queue processor error: {e}")
                time.sleep(15)
    
    def process_sync_queue(self):
        """Process pending sync operations from the queue"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            # Check if models are available
            if not models or 'SyncQueue' not in models:
                logger.warning("SyncQueue model not available, skipping queue processing")
                return

            SyncQueue = models['SyncQueue']

            with app.app_context():
                # Get pending sync items ordered by priority
                pending_syncs = SyncQueue.query.filter_by(
                    status='pending'
                ).order_by(
                    SyncQueue.priority.asc(),
                    SyncQueue.created_at.asc()
                ).limit(self.max_concurrent_syncs).all()

                for sync_item in pending_syncs:
                    if len(self.sync_threads) >= self.max_concurrent_syncs:
                        break

                    # Mark as processing
                    sync_item.status = 'processing'
                    sync_item.started_at = datetime.utcnow()
                    db.session.commit()

                    # Start sync thread
                    thread = Thread(
                        target=self._execute_sync_item,
                        args=(sync_item.id,),
                        daemon=True
                    )
                    thread.start()
                    self.sync_threads[sync_item.id] = thread
                
                # Clean up completed threads
                completed_threads = []
                for sync_id, thread in self.sync_threads.items():
                    if not thread.is_alive():
                        completed_threads.append(sync_id)
                
                for sync_id in completed_threads:
                    del self.sync_threads[sync_id]
                    
        except Exception as e:
            logger.error(f"Error processing sync queue: {e}")
    
    def _execute_sync_item(self, sync_item_id: int):
        """Execute a single sync item"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            if not models or 'SyncQueue' not in models:
                logger.warning("SyncQueue model not available")
                return

            SyncQueue = models['SyncQueue']

            with app.app_context():
                sync_item = SyncQueue.query.get(sync_item_id)
                if not sync_item:
                    return
                
                logger.info(f"Executing sync item {sync_item_id}: {sync_item.operation}")
                
                success = False
                error_message = None
                
                try:
                    if sync_item.operation == 'update_inventory':
                        success = self._sync_inventory_update(sync_item)
                    elif sync_item.operation == 'sync_all':
                        success = self._sync_all_channels(sync_item)
                    elif sync_item.operation == 'update_price':
                        success = self._sync_price_update(sync_item)
                    else:
                        error_message = f"Unknown operation: {sync_item.operation}"
                
                except Exception as e:
                    error_message = str(e)
                    logger.error(f"Sync item {sync_item_id} failed: {e}")
                
                # Update sync item status
                sync_item.status = 'completed' if success else 'failed'
                sync_item.completed_at = datetime.utcnow()
                sync_item.error_message = error_message
                
                if not success:
                    sync_item.retry_count += 1
                    
                    # Schedule retry if within retry limit
                    if sync_item.retry_count < len(self.retry_delays):
                        retry_delay = self.retry_delays[sync_item.retry_count - 1]
                        sync_item.next_retry_at = datetime.utcnow() + timedelta(seconds=retry_delay)
                        sync_item.status = 'pending'
                        logger.info(f"Scheduling retry for sync item {sync_item_id} in {retry_delay} seconds")
                
                db.session.commit()
                
        except Exception as e:
            logger.error(f"Error executing sync item {sync_item_id}: {e}")
    
    def _sync_inventory_update(self, sync_item) -> bool:
        """Sync inventory update to external channel"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            if not models:
                logger.warning("Database models not available")
                return False

            SalesChannel = models.get('SalesChannel')
            Product = models.get('Product')
            InventorySyncLog = models.get('InventorySyncLog')
            ChannelInventory = models.get('ChannelInventory')

            if not SalesChannel or not Product:
                logger.warning("Required models not available")
                return False

            channel = SalesChannel.query.get(sync_item.channel_id)
            if not channel or not channel.sync_enabled:
                return False

            product = Product.query.get(sync_item.product_id)
            if not product:
                return False
            
            # Get sync data
            data = sync_item.data or {}
            new_quantity = data.get('new_quantity', product.stock_quantity)

            # Create sync log if model is available
            sync_log = None
            if InventorySyncLog:
                sync_log = InventorySyncLog(
                    product_id=sync_item.product_id,
                    variant_id=sync_item.variant_id,
                    channel_id=sync_item.channel_id,
                    sync_type='push',
                    operation='update',
                    old_quantity=data.get('old_quantity', 0),
                    new_quantity=new_quantity,
                    quantity_change=new_quantity - data.get('old_quantity', 0),
                    status='processing',
                    started_at=datetime.utcnow(),
                    request_data=data
                )
                db.session.add(sync_log)
                db.session.flush()
            
            # Simulate external API call (replace with actual API integration)
            success = self._call_external_api(channel, 'update_inventory', {
                'product_id': sync_item.product_id,
                'variant_id': sync_item.variant_id,
                'quantity': new_quantity
            })
            
            # Update sync log
            sync_log.status = 'success' if success else 'failed'
            sync_log.completed_at = datetime.utcnow()
            
            if success:
                # Update channel inventory if model is available
                if ChannelInventory:
                    channel_inv = ChannelInventory.query.filter_by(
                        channel_id=sync_item.channel_id,
                        product_id=sync_item.product_id,
                        variant_id=sync_item.variant_id
                    ).first()

                    if channel_inv:
                        channel_inv.quantity = new_quantity
                        channel_inv.available_quantity = max(0, new_quantity - channel_inv.reserved_quantity)
                        channel_inv.last_synced_at = datetime.utcnow()
                        channel_inv.sync_status = 'synced'
                        channel_inv.sync_error = None
                
                # Update channel status
                channel.last_sync_at = datetime.utcnow()
                channel.error_count = 0
                channel.status = 'active'
            else:
                # Update error count
                channel.error_count += 1
                if channel.error_count >= 5:
                    channel.status = 'error'
                    logger.warning(f"Channel {channel.name} marked as error due to repeated failures")
            
            db.session.commit()
            return success
            
        except Exception as e:
            logger.error(f"Error syncing inventory update: {e}")
            return False
    
    def _sync_all_channels(self, sync_item) -> bool:
        """Sync product to all channels"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()
            functions = self._get_functions()

            if not models:
                logger.warning("Database models not available")
                return False

            Product = models.get('Product')
            SalesChannel = models.get('SalesChannel')
            queue_inventory_sync = functions.get('queue_inventory_sync') if functions else None

            if not Product or not SalesChannel:
                logger.warning("Required models not available")
                return False

            product = Product.query.get(sync_item.product_id)
            if not product:
                return False

            active_channels = SalesChannel.query.filter_by(
                is_active=True,
                sync_enabled=True
            ).all()
            
            success_count = 0
            total_channels = len(active_channels)

            for channel in active_channels:
                if channel.id == sync_item.channel_id:
                    continue  # Skip source channel

                # Queue individual sync if function is available
                if queue_inventory_sync:
                    queue_success, _ = queue_inventory_sync(
                        product_id=sync_item.product_id,
                        variant_id=sync_item.variant_id,
                        channel_id=channel.id,
                        operation='update_inventory',
                        data={
                            'new_quantity': product.stock_quantity,
                            'source': 'bulk_sync'
                        },
                        priority=3
                    )
                
                if queue_success:
                    success_count += 1
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error syncing all channels: {e}")
            return False
    
    def _sync_price_update(self, sync_item) -> bool:
        """Sync price update to external channel"""
        # Implementation for price sync
        # This would be similar to inventory sync but for prices
        return True

    def _call_external_api(self, channel, operation: str, data: Dict) -> bool:
        """
        Call external channel API
        This is a placeholder - implement actual API calls for each channel type
        """
        try:
            # Simulate API call delay
            time.sleep(0.5)
            
            # Simulate success/failure based on channel status
            if channel.status == 'error':
                return False
            
            # Simulate 95% success rate
            import random
            return random.random() < 0.95
            
        except Exception as e:
            logger.error(f"External API call failed for {channel.name}: {e}")
            return False
    
    def check_scheduled_syncs(self):
        """Check for channels that need scheduled sync"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            if not models or 'SalesChannel' not in models:
                logger.warning("SalesChannel model not available")
                return

            SalesChannel = models['SalesChannel']
            Product = models.get('Product')

            with app.app_context():
                now = datetime.utcnow()

                channels_to_sync = SalesChannel.query.filter(
                    SalesChannel.is_active == True,
                    SalesChannel.sync_enabled == True,
                    SalesChannel.next_sync_at <= now
                ).all()
                
                for channel in channels_to_sync:
                    logger.info(f"Triggering scheduled sync for channel: {channel.name}")

                    # Get products to sync if Product model is available
                    if Product:
                        products = Product.query.filter(Product.stock_quantity >= 0).all()

                        for product in products:
                            # Use the queue function if available
                            functions = self._get_functions()
                            if functions and 'queue_inventory_sync' in functions:
                                functions['queue_inventory_sync'](
                                    product_id=product.id,
                                    channel_id=channel.id,
                                    operation='update_inventory',
                                    data={
                                        'new_quantity': product.stock_quantity,
                                        'source': 'scheduled_sync'
                                    },
                                    priority=4
                                )

                    # Update next sync time
                    channel.next_sync_at = now + timedelta(seconds=channel.sync_frequency)

                db.session.commit()
                
        except Exception as e:
            logger.error(f"Error checking scheduled syncs: {e}")
    
    def retry_failed_syncs(self):
        """Retry failed sync operations"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            if not models or 'SyncQueue' not in models:
                logger.warning("SyncQueue model not available")
                return

            SyncQueue = models['SyncQueue']

            with app.app_context():
                now = datetime.utcnow()

                failed_syncs = SyncQueue.query.filter(
                    SyncQueue.status == 'pending',
                    SyncQueue.retry_count > 0,
                    SyncQueue.next_retry_at <= now
                ).all()

                for sync_item in failed_syncs:
                    logger.info(f"Retrying sync item {sync_item.id} (attempt {sync_item.retry_count + 1})")
                    sync_item.next_retry_at = None

                db.session.commit()
                
        except Exception as e:
            logger.error(f"Error retrying failed syncs: {e}")
    
    def cleanup_old_logs(self):
        """Clean up old sync logs"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            if not models:
                logger.warning("Database models not available")
                return

            InventorySyncLog = models.get('InventorySyncLog')
            SyncQueue = models.get('SyncQueue')

            with app.app_context():
                # Keep logs for 30 days
                cutoff_date = datetime.utcnow() - timedelta(days=30)

                old_logs = []
                if InventorySyncLog:
                    old_logs = InventorySyncLog.query.filter(
                        InventorySyncLog.created_at < cutoff_date
                    ).all()

                    for log in old_logs:
                        db.session.delete(log)

                # Keep completed sync queue items for 7 days
                cutoff_date = datetime.utcnow() - timedelta(days=7)

                old_queue_items = []
                if SyncQueue:
                    old_queue_items = SyncQueue.query.filter(
                        SyncQueue.status == 'completed',
                        SyncQueue.completed_at < cutoff_date
                    ).all()

                    for item in old_queue_items:
                        db.session.delete(item)

                db.session.commit()
                logger.info(f"Cleaned up {len(old_logs)} old sync logs and {len(old_queue_items)} old queue items")
                
        except Exception as e:
            logger.error(f"Error cleaning up old logs: {e}")
    
    def detect_and_resolve_conflicts(self):
        """Detect and auto-resolve inventory conflicts"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()
            functions = self._get_functions()

            if not models or 'Product' not in models:
                logger.warning("Product model not available")
                return

            Product = models['Product']
            detect_inventory_conflicts = functions.get('detect_inventory_conflicts') if functions else None

            with app.app_context():
                # Get all products with potential conflicts
                products = Product.query.all()

                for product in products:
                    if detect_inventory_conflicts:
                        conflicts = detect_inventory_conflicts(product.id)

                        if conflicts:
                            logger.info(f"Detected {len(conflicts)} conflicts for product {product.id}")

                            # Auto-resolve simple conflicts
                            for conflict_data in conflicts:
                                if conflict_data.get('type') != 'error':
                                    self._auto_resolve_conflict(product.id, conflict_data)
                
        except Exception as e:
            logger.error(f"Error detecting conflicts: {e}")
    
    def _auto_resolve_conflict(self, product_id: int, conflict_data: Dict):
        """Auto-resolve simple inventory conflicts"""
        try:
            # Only auto-resolve low-impact conflicts
            if abs(conflict_data.get('difference', 0)) <= 5:
                # Use master inventory as source of truth
                master_quantity = conflict_data.get('master_quantity', 0)

                # Try to get the inventory update function
                functions = self._get_functions()
                update_inventory_func = functions.get('update_inventory_with_sync') if functions else None

                if update_inventory_func:
                    success, message = update_inventory_func(
                        product_id=product_id,
                        new_quantity=master_quantity,
                        reason="Auto-resolved conflict",
                        broadcast=False
                    )

                    if success:
                        logger.info(f"Auto-resolved conflict for product {product_id}")
                else:
                    logger.warning("Inventory update function not available for conflict resolution")
                
        except Exception as e:
            logger.error(f"Error auto-resolving conflict: {e}")
    
    def health_check(self):
        """Perform health check on sync system"""
        try:
            app, db = self._get_app_components()
            models = self._get_models()

            if not models:
                logger.warning("Database models not available for health check")
                return

            SyncQueue = models.get('SyncQueue')
            SalesChannel = models.get('SalesChannel')

            with app.app_context():
                # Check queue size
                pending_count = processing_count = failed_count = 0
                if SyncQueue:
                    pending_count = SyncQueue.query.filter_by(status='pending').count()
                    processing_count = SyncQueue.query.filter_by(status='processing').count()
                    failed_count = SyncQueue.query.filter_by(status='failed').count()

                # Check channel health
                error_channels = 0
                if SalesChannel:
                    error_channels = SalesChannel.query.filter_by(status='error').count()

                logger.info(f"Health Check - Queue: {pending_count} pending, {processing_count} processing, {failed_count} failed")
                logger.info(f"Health Check - Channels: {error_channels} in error state")

                # Alert if too many items in queue
                if pending_count > 1000:
                    logger.warning(f"High queue size: {pending_count} pending items")

                # Alert if too many failed channels
                if error_channels > 0:
                    logger.warning(f"Channels in error state: {error_channels}")
                
        except Exception as e:
            logger.error(f"Error in health check: {e}")

# Global scheduler instance
scheduler = InventorySyncScheduler()

def start_scheduler():
    """Start the inventory sync scheduler"""
    scheduler.start()

def stop_scheduler():
    """Stop the inventory sync scheduler"""
    scheduler.stop()

if __name__ == "__main__":
    # Run scheduler as standalone process
    start_scheduler()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        stop_scheduler()
