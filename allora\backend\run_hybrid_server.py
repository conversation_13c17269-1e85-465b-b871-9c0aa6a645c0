# Create: run_windows_hybrid.py
#!/usr/bin/env python3
"""
Windows-Compatible Hybrid Server for Allora
==========================================

Optimized for Windows with same performance benefits as Gunicorn+Eventlet
"""

import sys
import os
import time
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration
HOST = os.getenv('HOST', '127.0.0.1')
PORT = int(os.getenv('PORT', 5000))
THREADS = int(os.getenv('THREADS', 6))

def print_startup_banner():
    """Print startup banner"""
    print("=" * 60)
    print("🚀 ALLORA WINDOWS HYBRID SERVER")
    print("=" * 60)
    print(f"📅 Startup Time: {datetime.now().isoformat()}")
    print(f"🌐 Host: {HOST}:{PORT}")
    print(f"💻 Platform: Windows")
    print("=" * 60)

def check_dependencies():
    """Check available server options for Windows"""
    deps = {
        'gevent': False,
        'eventlet': False,
        'socketio': False,
        'waitress': True
    }
    
    try:
        import gevent
        deps['gevent'] = True
    except ImportError:
        pass
    
    try:
        import eventlet
        deps['eventlet'] = True
    except ImportError:
        pass
    
    try:
        from app import socketio
        deps['socketio'] = socketio is not None
    except ImportError:
        pass
    
    return deps

def run_gevent_server(app):
    """Run with Gevent WSGI Server (Best for Windows)"""
    try:
        import gevent
        from gevent import monkey
        monkey.patch_all()  # Make standard library async-compatible
        
        from gevent import pywsgi
        from geventwebsocket.handler import WebSocketHandler
        
        print("🚀 Starting with Gevent WSGI Server")
        print("⚡ Performance: 10,000+ concurrent connections")
        print("🔌 WebSocket: Full SocketIO support")
        print("💻 Windows: Fully compatible")
        print("✅ Production-ready server")
        print("-" * 40)
        
        # Create Gevent WSGI server with WebSocket support
        server = pywsgi.WSGIServer(
            (HOST, PORT),
            app,
            handler_class=WebSocketHandler,
            log=None,  # Disable gevent's default logging
            error_log=None
        )
        
        print(f"🌐 Server running at: http://{HOST}:{PORT}")
        print("🔌 WebSocket endpoint: ws://{HOST}:{PORT}/socket.io/")
        print("📊 Health check: http://{HOST}:{PORT}/api/health")
        print("🧪 SocketIO test: http://{HOST}:{PORT}/socketio-test")
        print("=" * 60)
        
        server.serve_forever()
        return True
        
    except ImportError as e:
        print(f"❌ Gevent not available: {e}")
        print("💡 Install with: pip install gevent gevent-websocket")
        return False
    except Exception as e:
        print(f"❌ Gevent server failed: {e}")
        return False

def run_eventlet_server(app):
    """Run with Eventlet WSGI Server (Alternative for Windows)"""
    try:
        import eventlet
        eventlet.monkey_patch()  # Make standard library async-compatible
        
        import eventlet.wsgi
        
        print("🔌 Starting with Eventlet WSGI Server")
        print("⚡ Performance: 5,000+ concurrent connections")
        print("🔌 WebSocket: Full SocketIO support")
        print("💻 Windows: Compatible")
        print("✅ Production-ready server")
        print("-" * 40)
        
        # Create Eventlet WSGI server
        listener = eventlet.listen((HOST, PORT))
        
        print(f"🌐 Server running at: http://{HOST}:{PORT}")
        print("=" * 60)
        
        eventlet.wsgi.server(listener, app, log_output=False)
        return True
        
    except ImportError as e:
        print(f"❌ Eventlet not available: {e}")
        print("💡 Install with: pip install eventlet")
        return False
    except Exception as e:
        print(f"❌ Eventlet server failed: {e}")
        return False

def run_socketio_server(app):
    """Run with Flask-SocketIO server (Current working solution)"""
    try:
        from app import socketio
        if not socketio:
            return False
            
        print("🔌 Starting with Flask-SocketIO server")
        print("⚡ Performance: 1,000+ concurrent connections")
        print("🔌 WebSocket: Full SocketIO support")
        print("💻 Windows: Fully compatible")
        print("⚠️  Note: Development server (consider upgrading)")
        print("-" * 40)
        
        socketio.run(
            app, 
            host=HOST, 
            port=PORT, 
            debug=False,
            allow_unsafe_werkzeug=True
        )
        return True
        
    except Exception as e:
        print(f"❌ SocketIO server failed: {e}")
        return False

def run_waitress_server(app):
    """Run with Waitress (HTTP-only fallback)"""
    try:
        from waitress import serve
        
        print("⚠️  Starting with Waitress (HTTP-only)")
        print("⚡ Performance: 500+ concurrent connections")
        print("❌ WebSocket: SocketIO features DISABLED")
        print("💻 Windows: Fully compatible")
        print("💡 Install async server for SocketIO support")
        print("-" * 40)
        
        serve(app, host=HOST, port=PORT, threads=THREADS)
        return True
        
    except Exception as e:
        print(f"❌ Waitress server failed: {e}")
        return False

def initialize_app():
    """Initialize the Flask application"""
    try:
        from app import app, db
        
        print("🔧 Initializing application...")
        
        with app.app_context():
            from app import (
                initialize_payment_gateways, 
                initialize_admin_user, 
                initialize_oauth_providers
            )
            
            try:
                db.create_all()
                print("✅ Database tables created/verified")
            except Exception as e:
                print(f"⚠️  Database warning: {e}")
            
            try:
                initialize_payment_gateways()
                print("✅ Payment gateways initialized")
            except Exception as e:
                print(f"⚠️  Payment gateways warning: {e}")
            
            try:
                initialize_admin_user()
                print("✅ Admin user initialized")
            except Exception as e:
                print(f"⚠️  Admin user warning: {e}")
            
            try:
                initialize_oauth_providers()
                print("✅ OAuth providers initialized")
            except Exception as e:
                print(f"⚠️  OAuth providers warning: {e}")
        
        return app
        
    except Exception as e:
        print(f"❌ Application initialization failed: {e}")
        return None

def main():
    """Main server selection logic for Windows"""
    print_startup_banner()
    
    # Check available dependencies
    deps = check_dependencies()
    print("🔍 Checking Windows-compatible servers...")
    print(f"   Gevent: {'✅' if deps['gevent'] else '❌'}")
    print(f"   Eventlet: {'✅' if deps['eventlet'] else '❌'}")
    print(f"   SocketIO: {'✅' if deps['socketio'] else '❌'}")
    print(f"   Waitress: {'✅' if deps['waitress'] else '❌'}")
    print()
    
    # Initialize application
    app = initialize_app()
    if not app:
        print("❌ Failed to initialize application")
        sys.exit(1)
    
    route_count = len(list(app.url_map.iter_rules()))
    print(f"📍 Total API routes registered: {route_count}")
    print("=" * 60)
    
    # Windows-optimized server selection
    print("🎯 Selecting optimal Windows server...")
    
    # Option 1: Try Gevent (Best performance for Windows)
    if deps['gevent']:
        if run_gevent_server(app):
            return
    else:
        print("⚠️  Gevent unavailable")
        print("💡 Install with: pip install gevent gevent-websocket")
    
    # Option 2: Try Eventlet (Good performance)
    if deps['eventlet']:
        if run_eventlet_server(app):
            return
    else:
        print("⚠️  Eventlet unavailable")
        print("💡 Install with: pip install eventlet")
    
    # Option 3: Try SocketIO server (Current working)
    if deps['socketio']:
        if run_socketio_server(app):
            return
    else:
        print("⚠️  SocketIO server unavailable")
    
    # Option 4: Fallback to Waitress (HTTP-only)
    if deps['waitress']:
        if run_waitress_server(app):
            return
    
    print("❌ All server options failed!")
    sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)